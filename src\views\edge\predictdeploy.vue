<template>
  <div class="mod-edge__predictdeploy">
    <div class="predictdeploy-form-card">
      <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
        <!-- 检索 -->
        <el-row align="middle" style="margin-bottom: 10px; flex-wrap: wrap;">
          <span style="font-weight: bold; margin-right: 12px; display: flex; align-items: center; height: 32px;">检索：</span>
          <el-form-item label="" style="margin-right: 12px">
            <el-input v-model="state.dataForm.deviceId" placeholder="请输入设备ID" style="width: 150px"></el-input>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-select clearable class="m-2" v-model="state.dataForm.nodeId" value-key="id" placeholder="请选择节点" style="width: 150px">
              <el-option v-for="item in nodeInfo" :key="item.id" :label="item.sname" :value="item.saddress" />
            </el-select>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-select v-model="state.dataForm.gId" placeholder="请选择分组" clearable filterable style="width: 150px">
              <el-option v-for="group in searchGroupList" :key="group.id" :label="group.gName" :value="group.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-button :icon="Search" @click="state.getDataList()"></el-button>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
          </el-form-item>
        </el-row>
        <!-- 策略 -->
        <el-row align="middle" style="margin-bottom: 18px; flex-wrap: wrap;">
          <span style="font-weight: bold; margin-right: 12px; display: flex; align-items: center; height: 32px;">策略：</span>
          <el-form-item label="" style="margin-right: 12px">
            <el-select v-model="value" placeholder="选择轮询算法" style="width: 150px" multiple clearable>
              <el-option v-for="item in al" :key="item.id" :label="item.trainName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-input v-model="num" placeholder="单次轮询算法个数" style="width: 150px" type="number" @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-input v-model="time" placeholder="轮询时间/分钟" style="width: 150px"></el-input>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-button type="primary" plain @click="startPoll()" :disabled="isStart">启动轮询</el-button>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px">
            <el-button type="primary" plain @click="stopPoll()">删除轮询</el-button>
          </el-form-item>
        </el-row>
        <!-- 分组策略 -->
        <!-- <el-row align="middle" style="margin-bottom: 18px; flex-wrap: wrap;">
          <span style="font-weight: bold; margin-right: 12px; display: flex; align-items: center; height: 32px;">分组策略：</span>
          <el-form-item label="" style="margin-right: 12px; min-width: 180px;">
            <el-select v-model="groupStrategyParam.groupId" placeholder="选择分组" style="width: 180px" clearable filterable>
              <el-option v-for="group in searchGroupList" :key="group.id" :label="group.gName" :value="group.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px; min-width: 320px; display: flex; align-items: center;">
            <el-date-picker
              v-model="groupStrategyParam.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 200px"
              value-format="YYYY-MM-DD"
            />
            <el-time-picker
              v-model="groupStrategyParam.timeRange"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 200px; margin-left: 8px;"
              value-format="HH:mm:ss"
            />
          </el-form-item>
          <div style="margin: 0 8px; color: #bbb; font-weight: bold; font-size: 16px; line-height: 40px; display: flex; align-items: center;">or</div>
          <el-form-item label="" style="margin-right: 12px; min-width: 200px;">
            <el-input v-model="groupStrategyParam.cron" placeholder="cron表达式" style="width: 200px" />
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px;">
            <el-button type="primary" plain @click="startGroupTask" style="min-width: 90px;">启动任务</el-button>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" plain @click="deleteGroupTask" style="min-width: 90px;">删除任务</el-button>
          </el-form-item>
        </el-row> -->
        <!-- 分组启停 -->
        <el-row align="middle" style="margin-bottom: 18px; flex-wrap: wrap;">
          <span style="font-weight: bold; margin-right: 12px; display: flex; align-items: center; height: 32px;">分组启停：</span>
          <el-form-item label="" style="margin-right: 12px; min-width: 180px;">
            <el-select v-model="groupControlParam.groupId" placeholder="选择分组" style="width: 180px" clearable filterable>
              <el-option v-for="group in searchGroupList" :key="group.id" :label="group.gName" :value="group.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="" style="margin-right: 12px;">
            <el-button type="primary" plain @click="startGroupControl" style="min-width: 90px;">启动</el-button>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" plain @click="stopGroupControl" style="min-width: 90px;">停止</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" :show-overflow-tooltip="true">
      <el-table-column type="selection" header-align="left" align="center" width="60"></el-table-column>
      <el-table-column prop="id" label="ID" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="gId" label="分组ID" header-align="center" align="center" width="180"></el-table-column> -->
      <el-table-column prop="gName" label="分组名称" header-align="center" align="center"></el-table-column>
      <!--              <el-table-column prop="trainId" label="训练ID" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="trainName" label="算法名" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="nodeId" label="节点ID" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="nodeName" label="节点名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="nodeIp" label="节点IP" header-align="center" align="center"></el-table-column>
      <el-table-column prop="imageId" label="算法镜像ID" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="openaiBaseUrl" label="OpenAI Base URL（MLLM）" header-align="center" align="center"></el-table-column>
      <el-table-column prop="openApiKey" label="OpenAI API Key（MLLM）" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="modelAfterTrainingUrl" label="镜像名称" header-align="center" align="center">
        <template #default="{ row }">
          {{ extractImageName(row.modelAfterTrainingUrl) }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="model" label="镜像名称" header-align="center" align="center"></el-table-column> -->
      <!-- <el-table-column prop="prompt" label="prompt 提示词（MLLM）" header-align="center" align="center"></el-table-column> -->
      
      <!-- <el-table-column prop="sendUrl" label="数据推送URL" header-align="center" align="center"></el-table-column>
      <el-table-column prop="videoInput" label="视频输入URL" header-align="center" align="center"></el-table-column> -->

      <el-table-column prop="deployStatus" width="95" :label="$t('edge.deployStatus')" header-align="center" align="center">
        <!-- 列标题上的提示框 -->
        <template v-slot:header>
          <el-tooltip effect="customized" :content="$t('edge.deployStatusContent')" placement="top" size="large">
            <span>{{ $t("edge.deployStatus") }}</span>
          </el-tooltip>
        </template>
        <!-- 使用新的 template 标签 -->
        <template #default="{ row }">
          <el-tag round :key="row.id" :type="getTagType(row.deployStatus)">
            {{ getDeployStatus(row.deployStatus) == "" ? "未知" : getDeployStatus(row.deployStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="securityLevel" width="100" :label="$t('edge.securityLevel')" header-align="center" align="center">
        <!-- 列标题上的提示框 -->
        <!-- <template v-slot:header>
                      <el-tooltip effect="customized" :content="$t('edge.safeTypeContent')" placement="top" size="large">
                        <span>{{ $t("edge.securityLevel") }}</span>
                      </el-tooltip>
                    </template> -->
        <!-- 使用新的 template 标签 -->
        <template #default="{ row }">
          <el-tag round :key="row.id" type="success">
            {{ sefeType(row.securityLevel) == "" ? "自然语言" : sefeType(row.securityLevel) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="查看详情" header-align="center" align="center" width="100px">
        <template #default="scope">
          <el-button type="primary" plain size="small" v-if="state.hasPermission('sys:user:delete')" @click="details(scope.row.id)">查看详情</el-button>
        </template>
      </el-table-column>
      <!--              <el-table-column prop="twfDeleted" label="删除时间" header-align="center" align="center"></el-table-column>-->
      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="90" :show-overflow-tooltip="false">
        <template v-slot="scope">
          <el-button size="primary" class="start-btn" link type="success" text :disabled="isStartDisabled(scope.row.deployStatus)" @click="startModelAction(scope.row.id, scope.row.sendUrl, scope.row.securityLevel)">{{ $t("start") }} </el-button>
          <el-button size="primary" link type="danger" text :disabled="isStopDisabled(scope.row.deployStatus, scope.row.id)" @click="stopModelAction(scope.row.id)">{{ $t("stop") }} </el-button>
          <!-- <el-button type="warning" link @click="openPlayer(scope.row.outputStream, scope.row.id)">{{ $t("play") }}</el-button> -->
          <el-button type="warning" link :disabled="isUpdateDisabled(scope.row.id)" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <!--          <el-button v-if="state.hasPermission('edge:predictdeploy:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>-->
          <el-button type="danger" link @click="state.deleteHandle(scope.row.id)">删除</el-button>
          <!--          <el-button v-if="state.hasPermission('edge:predictdeploy:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>

    <devicePlayer ref="devicePlayerRef"></devicePlayer>
  </div>

  <el-dialog v-model="dialogTableVisible" title="模型部署详情" width="800" style="height: 500px; overflow: auto">
    <table border="1" cellspacing="0" bordercolor="#ccc" width="100" style="height: 100px; text-align: center">
      <tbody v-for="item in gridData" :key="item.id">
        <tr>
          <td style="color: #327ae6; font-weight: bold">ID</td>
          <td>{{ item.id }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">镜像名</td>
          <td>{{ item.trainName }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">节点ID</td>
          <td>{{ item.nodeId }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">节点IP</td>
          <td>{{ item.nodeIp }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">节点名称</td>
          <td>{{ item.nodeName }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">模型名称</td>
          <td>{{ item.model }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">部署状态</td>
          <td>{{ getDeployStatus(item.deployStatus) }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">安全等级</td>
          <td>{{ sefeType(item.securityLevel) }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">部署容器ID</td>
          <td>{{ item.containerId }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">阈值</td>
          <td>{{ item.threshold }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">时间间隔</td>
          <td>{{ item.timeInter }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">分析帧</td>
          <td>{{ item.analysisFrame }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">设备ID</td>
          <td>{{ item.deviceId }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">摄像头ID</td>
          <td>{{ item.cameraId }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">摄像头位置信息</td>
          <td>{{ item.cameraLocal }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">感兴趣区域坐标</td>
          <td>{{ item.roi }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">创建时间</td>
          <td>{{ item.twfCreated }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">更新时间</td>
          <td>{{ item.twfModified }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">模型输出流地址</td>
          <td>{{ item.outputStream }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">模型url</td>
          <td class="last-td">{{ item.modelAfterTrainingUrl }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">数据推送URL</td>
          <td class="last-td">{{ item.sendUrl }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">视频输入URL</td>
          <td class="last-td">{{ item.videoInput }}</td>
        </tr>
      </tbody>
    </table>

    <br />
  </el-dialog>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, onMounted, onBeforeUnmount, onActivated } from "vue";
import AddOrUpdate from "./predictdeploy-add-or-update.vue";
import { RouteLocationRaw } from "vue-router";
import router from "@/router";
import { Delete, Edit, Search, Share, Upload } from "@element-plus/icons-vue";
import { mediaServerRegister } from "@/utils/media";
import devicePlayer from "@/views/device/devicePlayer.vue";
import baseService from "@/service/baseService";
import { ElMessage, rowProps } from "element-plus";
import { TagType } from "@/types/interface";
import { log } from "console";
import app from "@/constants/app";
import { getCameraGrouping, startGroupControl as apiStartGroupControl } from "@/utils/api";
import { AlarmTypeInfo } from "./alarmtype-add-or-update.vue";

const nodeInfo = ref<any[]>([]);
const alarmTypeInfo = ref<AlarmTypeInfo[]>([]);

// 加载节点信息
baseService.get("/edge/tnodeinfo/page", { limit: 10000 }).then((res) => {
  nodeInfo.value = [...nodeInfo.value, ...res.data.list];
});

// 加载安全预警类型信息
baseService.get("/edge/tmodelcategory/allByType", { type: "CV" }).then((res) => {
  alarmTypeInfo.value = res.data.list;
});
//根据id获取详情
const getDetail = (id: number) => {
  return state.dataList?.find((item) => item.id === id);
};
const dialogTableVisible = ref(false);
const gridData = ref();
//查看详情
const details = (id: number) => {
  const detail = getDetail(id);
  gridData.value = detail ? [detail] : []; // 包装在数组中
  dialogTableVisible.value = true;
};
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/edge/predictdeploy/page",
  getDataListIsPage: true,
  exportURL: "/edge/predictdeploy/export",
  deleteURL: "/edge/predictdeploy",
  orderField: "id",
  dataForm: {
    deviceId: "",
    nodeId: "",
    gId: ""
  }
});
//轮询的算法
const value = ref("");
//一次轮询的个数
const num = ref("");
//轮询的时间
const time = ref("");
//总算法列表
interface Algorithm {
  trainName: string;
  id: string;

  // 这里可以添加其他属性
}
const al = ref<Algorithm[]>([]);
const isStart = ref(false);
baseService.get("/edge/predictdeploy/page", { limit: 100000 }).then((res) => {
  al.value = res.data.list;
  //过滤掉trainName含有"nlp"和"minicpmv"的算法

  al.value = al.value.filter((item) => !item.trainName.includes("nlp") && !item.trainName.includes("minicpmv") && !item.trainName.includes("多模态") && !item.trainName.includes("自然语言"));
});
onActivated(() => {
  baseService.get("/edge/poll").then((res) => {
    if (res.code === 0) {
      res.data.status == 0 ? (isStart.value = true) : (isStart.value = false);
    }
  });
});

const handleInput = (val: any) => {
  if (val > value.value.length) {
    ElMessage.warning("单次轮询算法个数不能超过总算法个数");
    num.value = "";
  }
  if (val < 1) {
    ElMessage.warning("单次轮询算法个数不能小于1");
    num.value = "";
  }
  if (val > 20) {
    ElMessage.warning("单次轮询算法个数不能超过20");
    num.value = "";
  }
};

const state = reactive({ ...useView(view), ...toRefs(view) });

// 保存原始的 getDataList
const originalGetDataList = state.getDataList;

// 覆写 getDataList 以添加 gName
state.getDataList = async () => {
  // 调用原始的 getDataList
  await originalGetDataList();

  // 确保 searchGroupList 已加载
  if (searchGroupList.value.length === 0) {
    await loadSearchGroups(); // 如果 searchGroupList 为空，尝试再次加载
  }

  // 创建 gId 到 gName 的映射表以便快速查找
  const groupMap = new Map(searchGroupList.value.map((group) => [group.id, group.gName]));

  // 遍历 state.dataList 并填充 gName
  if (state.dataList && Array.isArray(state.dataList)) {
    state.dataList = state.dataList.map((item) => {
      // 假设 item 中有 gId 字段
      // 如果没有 gId 但有 cameraId，您需要先通过 cameraId 获取 cameraInfo，再获取 gId
      const gName = item.gId ? groupMap.get(item.gId) : undefined;
      return { ...item, gName: gName || "" }; // 如果找不到gName，则设为空字符串
    });
  }
};

onMounted(() => {
  loadSearchGroups().then(() => {
    // 先确保分组列表加载完成
    state.getDataList(); // 然后加载数据列表
  });
});

onActivated(() => {
  loadSearchGroups().then(() => {
    // 同样，先确保分组列表加载完成
    state.getDataList();
  });
});

const getDeployStatus = (linkStatus: number): string => {
  let descriptions: TagType = {
    0: "准备中",
    1: "部署中",
    2: "部署完成",
    3: "运行中",
    4: "节点停止",
    99: "节点异常"
    // Add more descriptions as needed
  };
  return descriptions[linkStatus] || "";
};
const startPoll = () => {
  if (value.value.length === 0 || num.value === "" || time.value === "") {
    ElMessage.warning("请选择轮询算法，单次轮询个数，轮询时间");
  } else {
    //发送轮询请求
    ElMessage.success("轮询已启动");
    isStart.value = true;
    baseService
      .post("edge/predictdeploy/polling", {
        alList: value.value,
        params: num.value,
        time: time.value
      })
      .then((res) => {});
  }
};
const stopPoll = () => {
  baseService.put("edge/poll").then((res) => {
    if (res.code === 0) {
      ElMessage.success("轮询已停止");
      isStart.value = false;
    }
  });
};

const sefeType = (linkStatus: number): string => {
  // 优先使用动态加载的 alarmTypeInfo 数据
  if (alarmTypeInfo.value && alarmTypeInfo.value.length > 0) {
    const found = alarmTypeInfo.value.find(item => item.securityLevel === String(linkStatus));
    if (found && found.describeInfo) {
      return found.describeInfo;
    }
  }
  
  // 如果动态数据不可用，使用硬编码的备用数据
  let descriptions: TagType = {
    "1": "异常火点",
    "2": "异常烟雾",
    "3": "违规闯入",
    "4": "人员脱岗",
    "5": "人员摔倒",
    "6": "人员超限",
    "7": "人员趴睡",
    "8": "安全帽佩戴",
    "9": "工服穿戴",
    "10": "不按规定车道行驶",
    "11": "道路垃圾识别",
    "12": "车辆拥堵识别",
    "13": "车牌识别",
    "14": "机动车违停",
    "15": "车辆压线",
    "16": "车辆逆行",
    "17": "人员徘徊",
    "18": "偷倒渣土",
    "19": "雨水池污水口排放识别",
    "20": "车辆抛洒垃圾等杂物",
    "21": "人员攀爬",
    "22": "渣土车无顶盖、篷布识别",
    "23": "车辆超载违规",
    "24": "管廊管道有异物遮盖识别",
    "25": "危化品车辆识别",
    "26": "抽烟识别",
    "27": "打电话识别",
    "28": "玩手机",
    "29": "跌倒",
    "30": "洗手",
    "31": "拍照",
    "32": "消防通道占用",
    "33": " 通用文字识别",
    "34": "  中文分词",
    "35": "  关键词提取",
    "36": "文本相似度",
    "37": " 词性标注",
    "38": "情感分析",
    "39": " 实体识别",
    "40": " 文本分类",
    "41": " 文本摘要",
    "42": "人流量统计",
    "43": " 车流量统计",
    "44": " 机动车流量统计",
    "45": "非机动车流量统计",
    "46": "人群密度",
    "47": "车违规闯入",
    "49": "吃喝",
    "50": "占道经营"
    // Add more descriptions as needed
  };
  return descriptions[linkStatus] || "";
};
const getTagType = (linkStatus: number): string => {
  const tagTypes: TagType = {
    0: "warning",
    1: "warning",
    2: "success",
    3: "success",
    4: "danger",
    99: "danger"
    // 添加更多状态的标签类型
  };
  return tagTypes[linkStatus] || "";
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  // addOrUpdateRef.value.init(id);
  router.push({ path: "/edge/tcontroladdinfo", query: { id: id } });
};
const devicePlayerRef = ref();

//新增：用于存储分组列表的 ref 和 CameraGroupItem 接口
interface CameraGroupItem {
  id: string;
  gName: string;
}
const searchGroupList = ref<CameraGroupItem[]>([]);

//新增：加载分组列表的方法
const loadSearchGroups = async () => {
  try {
    const response = await getCameraGrouping();
    let actualData: CameraGroupItem[] = [];
    //假设 getCameraGrouping 返回的响应格式处理与 tcamerainfo.vue 中的类似
    if (response && typeof response.data !== "undefined" && typeof (response as any).code === "undefined") {
      if (response.data && response.data.code === 0 && Array.isArray(response.data.data)) {
        actualData = response.data.data;
      } else {
        console.error("加载搜索用分组列表失败 (AxiosResponse.data 格式错误):", response.data);
      }
    } else if (response && typeof (response as any).code === "number") {
      if ((response as any).code === 0 && Array.isArray((response as any).data)) {
        actualData = (response as any).data;
      } else {
        console.error("加载搜索用分组列表失败 (直接响应格式错误):", response);
      }
    } else if (Array.isArray(response)) {
      actualData = response;
    } else {
      console.error("加载搜索用分组列表失败: 未知响应格式", response);
    }
    searchGroupList.value = actualData;
  } catch (error) {
    console.error("加载搜索用摄像头分组列表时出错:", error);
    ElMessage.error("加载搜索用摄像头分组列表失败。");
    searchGroupList.value = [];
  }
};

const goToPage = (path: RouteLocationRaw) => {
  router.push(path);
};

const openPlayer = (streamUrl?: string, channelId?: string) => {
  mediaServerRegister(streamUrl, channelId);
  console.log(devicePlayer);
  devicePlayerRef.value.openDialog("streamPlay", {
    streamInfo: {
      streamServerIP: "",
      deviceID: "/live",
      channelId: "/" + channelId
    },
    hasAudio: true
  });
};
const isStartDisabled = (rowData: any) => {
  rowData = rowData === 3 ? true : false; // 如果 linkStatus 是 0，禁用启动按钮
  return rowData;
};

const isStopDisabled = (rowData: any, id: any) => {
  if (state.dataList?.find((item) => item.id === id)?.trainName.includes("minicpmv") || state.dataList?.find((item) => item.id === id)?.trainName.includes("多模态")) {
    return true;
  }
  rowData = rowData === 4 ? true : false;
  return rowData;
};

const isUpdateDisabled = (id: any) => {
  if (state.dataList?.find((item) => item.id === id)?.trainName.includes("minicpmv") || state.dataList?.find((item) => item.id === id)?.trainName.includes("多模态")) {
    return true;
  }
};

const startModelAction = (id: any, sendUrl: any, securityLevel: any) => {
  console.log(sendUrl.split("edge")[0].substring(0, sendUrl.split("edge")[0].length - 1), id, securityLevel);
  let ControlCommandDTO = {
    id,
    action: "start",
    path: "",
    params: ""
  };
  state.dataListLoading = true; // 显示加载动画
  //根据id查询imgName
  const trainName = state.dataList?.find((item) => item.id === id)?.trainName;
  if (trainName.includes("minicpmv") || trainName.includes("多模态")) {
    //发送id给python
    baseService
      .post(app.mini_api + "/minicpmv_analyse", {
        Id_number: id,
        Service_url: sendUrl.split("edge")[0].substring(0, sendUrl.split("edge")[0].length - 1),
        securitylevel_num: securityLevel
      })
      .then((res) => {
        console.log(res.code);
        if (res.code === 0) {
          //状态改为运行中
          baseService
            .put("/edge/predictdeploy/edit", {
              id: ControlCommandDTO.id,
              deployStatus: 3
            })
            .then((res) => {
              state.getDataList();
              state.dataListLoading = false;
            })
            .catch((err) => {
              state.dataListLoading = false;
            });
        } else {
          ElMessage.error({
            message: "启动失败",
            duration: 2000
          });
          state.dataListLoading = false;
        }
      })
      .catch((err) => {
        state.dataListLoading = false;
        return ElMessage({
          type: "error",
          message: "启动失败",
          duration: 2000
        });
      });
  } else {
    baseService.post(`/edge/predictdeploy/control`, ControlCommandDTO).then((res) => {
      console.log("res", res);
      if (res.code != 0) {
        return ElMessage({
          type: "error",
          message: res.msg,
          duration: 2000
        });
      } else {
        setTimeout(() => {
          ElMessage.success({
            message: "成功",
            duration: 500,
            onClose: () => {}
          });
          state.getDataList();
          state.dataListLoading = false;
        }, 5000);
      }
      // Object.assign(state.dataForm, res.data);
    });
  }
};

const stopModelAction = (id: any) => {
  let ControlCommandDTO = {
    id,
    action: "stop",
    path: "",
    params: ""
  };
  state.dataListLoading = true;
  baseService.post(`/edge/predictdeploy/control`, ControlCommandDTO).then((res) => {
    if (res.code != 0) {
      return ElMessage({
        type: "error",
        message: res.msg,
        duration: 2000
      });
    } else {
      setTimeout(() => {
        ElMessage.success({
          message: "成功",
          duration: 500,
          onClose: () => {}
        });
        state.getDataList();
        state.dataListLoading = false;
        console.log(ControlCommandDTO.id); //拿到id
      }, 5000);
    }
    // Object.assign(state.dataForm, res.data);
  });
  // setTimeout(() => {

  // }, 1000);
};

const groupStrategyParam = ref({
  groupId: '',
  dateRange: [], // [开始日期, 结束日期]
  timeRange: [], // [每天开始时间, 每天结束时间]
  cron: ''
});

const startGroupTask = () => {
  // TODO: 启动分组策略任务逻辑
  console.log(groupStrategyParam.value);
};
const deleteGroupTask = () => {
  // TODO: 删除分组策略任务逻辑
  console.log(groupStrategyParam.value);
};

const groupControlParam = ref({ groupId: '' });

const startGroupControl = async () => {
  const groupId = Number(groupControlParam.value.groupId);
  if (!groupId) {
    ElMessage.warning('请选择分组');
    return;
  }
  try {
    const res = await apiStartGroupControl({ groupId, controlType: 1 });
    // console.log(res);
    if (res.data.code === 0) {
      ElMessage.success('分组启动成功');
    } else {
      ElMessage.error(res.data.msg || '分组启动失败');
    }
  } catch (e) {
    ElMessage.error('分组启动请求异常');
  }
};
const stopGroupControl = async () => {
  const groupId = Number(groupControlParam.value.groupId);
  if (!groupId) {
    ElMessage.warning('请选择分组');
    return;
  }
  try {
    const res = await apiStartGroupControl({ groupId, controlType: 0 });

    if (res.data.code === 0) {
      ElMessage.success('分组停止成功');
    } else {
      ElMessage.error(res.data.msg || '分组停止失败');
    }
  } catch (e) {
    ElMessage.error('分组停止请求异常');
  }
};

// 提取镜像名称的函数
const extractImageName = (fullImageUrl: string): string => {
  if (!fullImageUrl) return '';

  // 从完整的镜像URL中提取镜像名称和标签
  // 例如: harbor.mybi.top:9180/algsets/safety_helmet_detection:dev_1.1
  // 提取出: safety_helmet_detection:dev_1.1

  const parts = fullImageUrl.split('/');
  if (parts.length > 0) {
    // 取最后一部分，即镜像名称和标签
    return parts[parts.length - 1];
  }

  return fullImageUrl;
};
</script>
<style>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}
.start-btn {
  margin-left: 8px;
}
table {
  width: 100%; /* 设置表格宽度为100% */
  max-width: 1200px; /* 设置表格的最大宽度 */
  border-collapse: collapse; /* 使用合并边框 */
  table-layout: fixed; /* 固定布局 */
}
th,
td {
  padding: 10px; /* 单元格内边距 */
  overflow: auto; /* 溢出隐藏 */
  white-space: nowrap; /* 不换行 */
}
.last-td {
  width: 100%;
}
.predictdeploy-form-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 18px 24px 8px 24px;
  margin-bottom: 18px;
}
.mod-edge__predictdeploy .el-row {
  margin-bottom: 12px !important;
}
.mod-edge__predictdeploy .el-form-item {
  margin-bottom: 0 !important;
}
.mod-edge__predictdeploy .el-button,
.mod-edge__predictdeploy .el-input,
.mod-edge__predictdeploy .el-select,
.mod-edge__predictdeploy .el-date-picker {
  vertical-align: middle;
}
</style>
