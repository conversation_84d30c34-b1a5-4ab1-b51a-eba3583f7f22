<template>
  <el-card shadow="never" class="aui-card--fill" v-loading="loading" element-loading-text="批量新增中...">
    <div class="mod-edge__tcontroladdinfo}">
      <el-row>
        <el-col type="flex" :xl="16" :lg="17">
          <el-row>
            <el-col>
              <el-col :xl="22" :lg="23">
                <div
                  style="width: 100%; height: 61.5vh; display: flex; flex-wrap: wrap; background-color: #f1f2f4; background-repeat: no-repeat; background-position: center 40%; background-size: 14% 25%"
                  :style="{ backgroundImage: 'url(' + logo + ')' }" ref="videoCanvasBox">
                  <jessibucaPlayer
                    v-if="videoUrl"
                    ref="jessibucaRef"
                    style="flex-grow: 1; background-color: #f1f2f4; background-repeat: no-repeat; background-position: center 40%; background-size: 14% 25%"
                    :videoUrl="videoUrl"
                    :error="videoError"
                    :message="videoError"
                    :hasAudio="hasAudio"
                    fluent
                    autoplay
                    live
                  ></jessibucaPlayer>
                  <canvas ref="videoCanvas" class="videoCanvas"></canvas>
                  <div class="video-seletion-tip">请在右侧选择视频</div>
                </div>
              </el-col>
            </el-col>
          </el-row>
          <el-row style="padding-top: 15px">
            <el-row :gutter="4">
              <el-button type="primary" icon="VideoPlay" :disabled="isShow" @click="playStart">
                {{ $t("edge.playVideo") }}
              </el-button>
              <el-button icon="VideoPause" @click="playStop">
                {{ $t("edge.stopVideo") }}
              </el-button>
              <el-button icon="Delete" type="danger" :disabled="isDrawing" @click="clearVideoCanvas">
                {{ $t("edge.clearControlArea") }}
              </el-button>
              <el-button icon="FullScreen" :type="isDrawing ? 'warning' : 'primary'" @click="startDraw">
                {{ $t("edge.addControlAreaRec") }}
              </el-button>
              <el-popover placement="right" trigger="click" title="布控坐标" :width="400">
                <template #reference>
                  <el-button icon="Rank" style="margin-left: 10px">
                    {{ $t("edge.viewAreaCoordinates") }}
                  </el-button>
                </template>
                <el-input type="textarea" placeholder="请绘制区域后再查看" v-model="coordinatesInfo"
                          autosize></el-input>
              </el-popover>
            </el-row>
          </el-row>
        </el-col>
        <el-col type="flex" :xl="8" :lg="7">
          <el-form label-width="100px" label-position="left" size="small" :model="forms" :rules="rules"
                   ref="dataFormRef">
            <el-form-item label="单选视频流" prop="videoStream" v-show="!isShow">
              <el-select filterable ref="itemSelect" v-model="forms.selectStreamUrl" placeholder="请单选视频流"
                         @change="setStreamInfo(getSelectedItem().id)">
                <el-option v-for="item in (state.dataList as any).list" :key="item.streamUrl" :label="item.name"
                           :value="item.streamUrl"></el-option>
              </el-select>
              &nbsp;<el-button @click="isShow = isShow ? false : true">{{
                isShow ? "切换到单选" : "切换到多选"
              }}
            </el-button>
            </el-form-item>

            <el-form-item label="多选视频流" prop="videoStream" v-if="isMutiple" v-show="isShow">
              <el-select filterable ref="itemSelect" v-model="selectedStreamUrlList" placeholder="请多选视频流"
                         @change="multiple" multiple>
                <el-option v-for="item in (state.dataList as any).list" :key="item.streamUrl" :label="item.name"
                           :value="item.streamUrl"></el-option>
              </el-select>
              &nbsp;<el-button @click="isShow = isShow ? false : true">{{
                isShow ? "切换到单选" : "切换到多选"
              }}
            </el-button>
            </el-form-item>
            <el-form-item label="算法" prop="trainName">
              <el-select class="m-2" v-model="forms.trainName" :disabled="istrainName" value-key="id" multiple
                         @change="onSelectImages" placeholder="请选择算法名称">
                <el-option v-for="item in imageInfo" :key="item.id" :label="item.imgName" :value="item.id"/>
              </el-select>
            </el-form-item>

            <el-form-item label="边缘设备">
              <el-select class="m-2" v-model="forms.nodeId" value-key="id" placeholder="请选择边缘设备"
                         @change="onSelectPush2">
                <el-option v-for="item in nodeInfo" :key="item.hostId" :label="item.hostname" :value="item.hostId"/>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="节点名称">
                           <el-input v-model="forms.nodeName"></el-input>
                         </el-form-item> -->

            <el-form-item prop="sendUrl" style="width: 282px; display: flex; align-items: center">
              <template #label>
                推送URL
                <el-tooltip class="box-item" effect="dark" content="报警信息推送网址" placement="top-start">
                  <el-icon style="margin-left: 8px; margin-top: 6px">
                    <QuestionFilled/>
                  </el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="forms.sname" class="m-2" value-key="id" :placeholder="$t('edge.selectPush')"
                         @change="onSelectPush">
                <el-option v-for="item in pushInfo" :key="item.id" :label="item.sname" :value="item.id"/>
              </el-select>
              <!-- <el-input v-model="forms.sendUrl" placeholder="数据推送URL"></el-input> -->
            </el-form-item>
            <el-form-item label="选择公司" v-model="forms.tenantId" v-if="!isShow2">
              <el-select filterable ref="itemSelect" v-model="forms.tenantId" placeholder="选择公司">
                <el-option v-for="item in (deptName as any)" :key="item.id" :label="item.name"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item prop="threshold" style="width: 282px; display: flex; align-items: center">
              <template #label>
                阈值
                <el-tooltip class="box-item" effect="dark" content="设定超过多少时间（秒）开始触发操作"
                            placement="top-start">
                  <el-icon style="margin-left: 8px; margin-top: 6px">
                    <QuestionFilled/>
                  </el-icon>
                  <el-icon></el-icon>
                </el-tooltip>
              </template>
              <el-input v-model="forms.threshold" style="flex: 1"></el-input>
            </el-form-item>
            <el-form-item prop="timeInter" style="width: 282px">
              <template #label>
                时间间隔
                <el-tooltip class="box-item" effect="dark" content="设定每隔多少时间（秒）推送报警" placement="top-start">
                  <el-icon style="margin-left: 8px; margin-top: 6px">
                    <QuestionFilled/>
                  </el-icon>
                </el-tooltip>
              </template>
              <el-input v-model="forms.timeInter" style="flex: 1"></el-input>
            </el-form-item>
            <el-form-item label="布控坐标" prop="roi">
              <el-input disabled v-model="forms.roi"></el-input>
            </el-form-item>

            <el-form-item label="安全预警模型" prop="securityLevel" v-if="islevel">
              <el-select class="m-2" v-model="forms.securityLevel" value-key="id" placeholder="请选择安全预警模型">
                <el-option v-for="item in alarmTypeInfo" :key="item.id" :label="item.describeInfo"
                           :value="item.securityLevel"/>
              </el-select>
            </el-form-item>

            <el-popover placement="bottom-start" title="" :popper-style="popoverStyles" trigger="click">
              <template #reference>
                <el-button class="m-2">更多</el-button>
              </template>
              <el-form-item label="模型输出流地址">
                <el-input v-model="forms.outputStream"></el-input>
              </el-form-item>
              <el-form-item label="分析帧" prop="analysisFrame">
                <el-input v-model="forms.analysisFrame"></el-input>
              </el-form-item>
              <el-form-item label="摄像头位置">
                <el-input v-model="forms.cameraLocal"></el-input>
              </el-form-item>
            </el-popover>
          </el-form>
          <br/>
          <el-button size="small" type="primary" :disabled="isDrawing" @click="dataSubmit()">部署</el-button>
          <!-- <el-button size="small" @click="mediaListInfo">取消</el-button> -->
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script lang="ts">
import {defineComponent, ref, onMounted, nextTick, computed, onActivated} from "vue";
import useView from "@/hooks/useView";
import jessibucaPlayer from "@/views/device/jessibuca.vue";
import Jessibuca from "@/views/device/jessibuca.vue";
import {mediaListInfo, mediaServerRegister} from "@/utils/media";
import {globalLanguage} from "@/utils/globaLang";
import {onUnmounted, reactive, toRefs} from "vue";
import baseService from "@/service/baseService";
import jessibuca from "@/views/device/jessibuca.vue";
import logo from "@/assets/images/logo.png";
import app from "@/constants/app";
import router from "@/router";
import {AlarmTypeInfo} from "@/views/edge/alarmtype-add-or-update.vue";
import {useRoute} from "vue-router";
import {get} from "http";
import {ElMessage, ElMessageBox} from "element-plus";
import type {Action} from "element-plus";

interface Forms {
  selectStreamUrl: string;
  modelUrl: string;
  modelName: string;
  modelBaseName: string;
  sendUrl: string;
  selectModel: string;
  roi: string;
  threshold: string;
  timeInter: string;
  analysisFrame: string;
  trainName: any;
  modelAfterTrainingUrl: string;
  nodeId: string;
  outputStream: string;
  nodeName: string;
  sname: string;
  containerId: string;
  predictPath: string;
  // 以下是新增字段
  imageId: string;
  securityLevel: string;
  platform: string;
  openaiBaseUrl: string;
  openApiKey: string;
  prompt: string;
  model: string;
// ====
  deviceName: string;
  cameraId: string;
  camPos: string;
  cameraLocal: string;
  nodeIp: string;
  list: any;
  selectStreamUrlList: any;
  tenantId: string;
  gId: string;
  gName: string;
}

interface StreamInfo {
  streamServerIP: string;
  deviceID: string;
  channelId: string;
}

interface ModelData {
  id: string;
  modelName: string;
  modelType: string;
  modelBaseName: string;
  modelBaseUrl: string;
  datasetsId: string;
  datasetsName: string;
  datasetsUrl: string;
  cmdTrain: string;
  configs: string;
  modelAfterTrainingUrl: string;
  nodeId: string;
  logs: string;
  memo: string;
  twfCreated: null;
  twfModified: null;
  twfDeleted: null;
}

/**
 * 主机运行状态信息（DashboardBase + DashboardCurrent 融合版）
 * 所有字段均保持与后端 JSON 一致，便于直接反序列化。
 */
interface DashboardSnapshot {

  /** 主机名 */
  hostname: string;

  /** 主机 UUID */
  hostId: string;

  /** 操作系统类型，例如 linux / windows */
  os: string;

  /** 发行版名称，例如 ubuntu */
  platform: string;

  /** 发行版家族，例如 debian */
  platformFamily: string;

  /** 发行版版本号，例如 20.04 */
  platformVersion: string;

  /** 内核架构，例如 x86_64 */
  kernelArch: string;

  /** 内核版本 */
  kernelVersion: string;

  /** IPv4 地址 */
  ipv4Addr: string;

  /** 系统代理配置 */
  systemProxy: string;

  /** 物理 CPU 核心数 */
  cpuCores: number;

  /** 逻辑 CPU 核心数（含超线程） */
  cpuLogicalCores: number;

  /** CPU 型号 */
  cpuModelName: string;

  /** 系统累计运行时长（秒） */
  uptime: string;

  /** 最近一次启动时间（YYYY-MM-DD HH:mm:ss） */
  timeSinceUptime: string;

  /** 当前进程数量 */
  procs: number;

  /** 1 分钟平均负载 */
  load1: number;

  /** 5 分钟平均负载 */
  load5: number;

  /** 15 分钟平均负载 */
  load15: number;

  /** 负载占用率 (%) */
  loadUsagePercent: number;

  /** CPU 使用率 (%) */
  cpuUsedPercent: number;

  /** CPU 已用核心数 */
  cpuUsed: number;

  /** CPU 总核心数（可用） */
  cpuTotal: number;

  /** 内存总量（字节） */
  memoryTotal: string;

  /** 可用内存（字节） */
  memoryAvailable: string;

  /** 已用内存（字节） */
  memoryUsed: string;

  /** 内存使用率 (%) */
  memoryUsedPercent: number;

}


class MyPoint {
  constructor(public x1: number, public y1: number) {
  }
}

class MyLine {
  constructor(public x1: number, public y1: number, public x2: number | null = null, public y2: number | null = null) {
  }

  LineUpdate(x1: number, y1: number) {
    this.x2 = x1;
    this.y2 = y1;
  }

  LineDraw(ctx: CanvasRenderingContext2D) {
    ctx.lineWidth = 2;
    ctx.strokeStyle = "red";
    ctx.beginPath();
    ctx.moveTo(this.x1, this.y1);
    ctx.lineTo(this.x2!, this.y2!);
    ctx.stroke();
  }
}

interface PushInfo {
  id: string | number;
  sprotoName?: string;
  sname?: string;
  susername?: string;
  spassword?: string;
  stoken?: string;
  nodeName: string;
  nodeId: string;
}

// 定义一个接口用于线段的数据结构
interface Segment {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export interface ImagesInfo {
  id: string | number;
  imgName?: string;
  imgUrl?: string;
  type?: string;
  label?: string;
  model?: string;
  platform?: string;
  openai_base_url?: string;
  open_api_key?: string;
  prompt?: string;
  memo?: string;
}

interface Point {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export default defineComponent({
  methods: {mediaListInfo},
  components: {
    Jessibuca,
    jessibucaPlayer
  },
  setup() {
    const dataFormRef = ref();
    const visible = ref(false);
    const imageInfo = ref<ImagesInfo[]>([]); // 镜像列表
    const pushInfo = ref<PushInfo[]>([]); // 推送列表
    const {$t} = globalLanguage();
    const view = reactive({
      getDataListURL: "/edge/tcamerainfo/page",
      dataForm: {
        id: "",
        limit: 10000
      }
    });
    const state = reactive({...useView(view), ...toRefs(view)});
    const jessibucaRef = ref<InstanceType<typeof jessibuca> | null>(null);
    const videoUrl = ref("");
    const tabActiveName = ref("media");
    const channelId = ref("");
    const streamId = ref("");
    const mediaServerId = ref("");
    const deviceName = ref("");
    const cameraId = ref("");
    //临时存储多选摄像头
    const selectedStreamUrlList = ref<string[]>([]);
    const camPos = ref("");
    const istrainName = ref(false);
    const streamInfo = ref<StreamInfo | null>(null);
    const videoInfo = ref<string | null>(null);
    const audioInfo = ref<string | null>(null);
    const streamArrInfo = ref<string | null>(null);
    const coordinatesInfo = ref<string | null>(null);
    const hasAudio = ref(false);
    const isChanged = ref(false);
    const isDrawing = ref(false);
    const islevel = ref(false);
    const isMutiple = ref(true);
    const polygonLineArray = ref<MyLine[]>([]);
    const polygonLineMaxCount = ref(5);
    const polygonLine = ref<MyLine | null>(null);
    const videoCanvas = ref<HTMLCanvasElement | null>(null);
    const videoCanvasCtx = ref<CanvasRenderingContext2D | null>(null);
    const videoCanvasBox = ref();
    const modelInfo = ref<ModelData[]>([]);
    // const nodeInfo = ref<NodeData[]>([]);
    const nodeInfo = ref<DashboardSnapshot[]>([]);
    const loading = ref(false);
    const alarmTypeInfo = ref<AlarmTypeInfo[]>([]); // 模型分类
    const route = useRoute();
    const isShow = ref(false);
    const isShow2 = ref(false);
    const deptName = ref("");
    // 注释掉租户列表请求，避免请求地址错误
    // baseService.get("/sys/tenant/list").then((res) => {
    //   deptName.value = res.data;
    //   if (deptName.value.length === 1) {
    //     isShow2.value = true;
    //   }
    // });
    const rules = {
      // videoStream: [
      //   {required: true, message: '请选择视频流', trigger: 'change'}
      // ],
      outputStream: [{required: true, message: "请输入模型输出流地址", trigger: "change"}],
      nodeId: [{required: true, message: "请选择节点ID", trigger: "change"}],
      sendUrl: [{required: true, message: "请选择推送地址", trigger: "change"}],
      securityLevel: [{required: true, message: "请选择安全预警模型", trigger: "change"}],
      trainName: [{required: true, message: "请选择镜像名称", trigger: "watch"}],

      threshold: [
        {required: true, message: "请输入阈值", trigger: "blur"}
        // {type: 'number', message: '阈值必须为数字', trigger: 'blur'}
      ],

      timeInter: [
        {required: true, message: "请输入时间间隔", trigger: "blur"}
        // {type: 'number', message: '时间间隔必须为数字', trigger: 'blur'}
      ],
      analysisFrame: [
        {required: true, message: "请输入分析帧", trigger: "blur"}
        // {type: 'number', message: '分析帧必须为数字', trigger: 'blur'}
      ],
      roi: [{required: true, message: "请绘制布控坐标", trigger: "blur"}]
    };

    // 函数用于提取并返回坐标列表（不包括第一个点）
    function extractCornersAsList(segments: Segment[]): number[] {
      // 创建一个空数组来存储平面坐标
      let flatList: number[] = [];

      // 检查是否有线段数据
      if (segments.length === 0) {
        return flatList;
      }

      // 遍历所有线段，只添加终点坐标
      for (const segment of segments) {
        // 将每个线段的终点以x, y形式扩展到列表中
        flatList.push(segment.x2, segment.y2);
      }

      // 如果只有一个点，则返回空数组
      if (flatList.length < 4) {
        return [];
      }

      // 由于起点被重复添加，我们需要移除重复的最后一个点
      // 比较最后一个点与第一个点是否相同
      const n = flatList.length;
      if (flatList[0] === flatList[n - 2] && flatList[1] === flatList[n - 1]) {
        flatList.pop(); // 移除重复的y坐标
        flatList.pop(); // 移除重复的x坐标
      }
      if (flatList.length != 8) {
        return [];
      }

      // 返回平面坐标列表
      return flatList;
    }

    // 获取全部信息 - 修改为只加载基本信息，算法配置改为实时查询
    const getAllInfo = () => {
      Promise.all([
        baseService.get("/edge/imagesinfo/page", {limit: 10000}), //加载算法基本列表（仅用于选择）
        baseService.get("/edge/dashboardsnapshot/page", {limit: 10000}), // 加载node列表
        baseService.get("/edge/tpushinfo/page", {limit: 10000}), //加载推送列表
        baseService.get("/edge/tmodelcategory/allByType", {type: "CV"}) // 加载模型分类
      ]).then(([images, nodes, tpushinfo, alarmTypeRes]) => {
        // 只保存算法的基本信息用于选择下拉框
        imageInfo.value = images.data.list.map((item: any) => ({
          id: item.id,
          imgName: item.imgName,
          // 只保留选择所需的基本字段
        }));
        nodeInfo.value = nodes.data.list;
        pushInfo.value = tpushinfo.data.list;
        alarmTypeInfo.value = alarmTypeRes.data.list;

        console.log("基本信息加载完成，算法配置将在选择时实时查询");
      });
    };

    getAllInfo();

    const arr = ref<any>([]);
    const minArr = ref<any>([]);
    const newArr = ref<any>([]);
    onMounted(() => {
      videoCanvas.value = document.querySelector(".videoCanvas") as HTMLCanvasElement;
      videoCanvasCtx.value = videoCanvas.value.getContext("2d");
      baseService.get("/edge/trainmodel/page", {limit: 10000}).then((res) => {
        // console.log(res.data);
        modelInfo.value = [...modelInfo.value, ...res.data.list];
      });
    });

    onUnmounted(() => {
    });

    function convertToString(data: Point[]): string {
      const coordinates: number[] = [];

      data.forEach((point, index) => {
        if (index === 0) {
          coordinates.push(point.x1, point.y1);
        }
        coordinates.push(point.x2, point.y2);
      });

      return JSON.stringify(coordinates);
    }

    const parsedCoordinatesInfo = computed(() => {
      if (coordinatesInfo.value) {
        return JSON.parse(coordinatesInfo.value) as Point[];
      }
      return null;
    });

    // 实时查询算法配置的API函数
    const fetchAlgorithmConfigs = async (ids: (string | number)[]): Promise<any[]> => {
      try {
        // 根据后端接口，参数应该是ids数组，使用params传递
        const response = await baseService.get("/edge/predictdeploy/info", {
          params: {
            ids: ids
          }
        });
        return response.data || [];
      } catch (error) {
        console.error("查询算法配置失败:", error);
        ElMessage.error("查询算法配置失败");
        return [];
      }
    };

    // 选择镜像 - 改为实时查询配置
    const onSelectImages = async (value: any) => {
      console.log("选择算法ID:", value);

      if (!value || value.length === 0) {
        forms.value.list = [];
        return;
      }

      try {
        // 实时查询选中算法的配置信息
        const algorithmConfigs = await fetchAlgorithmConfigs(value);
        console.log("实时查询到的算法配置:", algorithmConfigs);

        if (algorithmConfigs && algorithmConfigs.length > 0) {
          // 处理查询到的配置信息
          const processedConfigs = algorithmConfigs.map((config: any) => {
            // 从 alarmTypeInfo 中根据 type 查找对应的平台信息
            const typeInfo = alarmTypeInfo.value.find((info) => info.securityLevel === config.securityLevel);
            let platform = typeInfo ? typeInfo.describeInfo : "";

            // 增强平台检测逻辑
            if (!platform) {
              if (config.model?.includes("mllm") || config.trainName?.includes("mllm") ||
                  config.trainName?.includes("多模态") || config.trainName?.includes("Qwen") ||
                  config.model?.includes("Qwen") || config.model?.includes("mllm")) {
                platform = "MLLM";
              }
            }

            // 为配置对象添加平台信息
            config.platform = platform;

            console.log("算法平台检测:", {
              trainName: config.trainName,
              model: config.model,
              securityLevel: config.securityLevel,
              typeInfo: typeInfo,
              finalPlatform: platform
            });

            // 检查是否需要显示安全预警模型选择
            if (config.trainName?.includes("minicpmv") || config.trainName?.includes("多模态")) {
              islevel.value = true;
            } else {
              islevel.value = false;
            }

            return config;
          });

          forms.value.list = processedConfigs;
          const firstConfig = processedConfigs[0];

          // 使用查询到的配置信息填充表单
          forms.value.model = firstConfig.model || "";
          forms.value.modelBaseName = firstConfig.model || "";
          forms.value.imageId = firstConfig.imageId || firstConfig.id || "";
          forms.value.platform = firstConfig.platform || "";
          forms.value.securityLevel = firstConfig.securityLevel || "";

          // 根据平台类型设置相关字段
          if (forms.value.platform === "MLLM") {
            // MLLM 平台特有字段
            forms.value.openaiBaseUrl = firstConfig.openaiBaseUrl || "";
            forms.value.openApiKey = firstConfig.openApiKey || "";
            forms.value.prompt = firstConfig.prompt || "";
          } else {
            // 非 MLLM 平台，清空 MLLM 特有字段
            forms.value.openaiBaseUrl = "";
            forms.value.openApiKey = "";
            forms.value.prompt = "";
          }

          console.log("实时查询后的表单数据:");
          console.log("platform:", forms.value.platform);
          console.log("model:", forms.value.model);
          console.log("modelBaseName:", forms.value.modelBaseName);
          console.log("securityLevel:", forms.value.securityLevel);
          console.log("imageId:", forms.value.imageId);
        } else {
          console.warn("未查询到算法配置信息");
          ElMessage.warning("未找到选中算法的配置信息");
        }
      } catch (error) {
        console.error("处理算法选择时出错:", error);
        ElMessage.error("处理算法选择时出错");
      }
    };

    const play = (stream_Info: StreamInfo, has_Audio: boolean) => {
      streamInfo.value = stream_Info;
      hasAudio.value = has_Audio;
      videoUrl.value = stream_Info.streamServerIP + stream_Info.deviceID + stream_Info.channelId + ".live.flv";
      playFromStreamInfo(false, videoUrl.value);
    };

    const playFromStreamInfo = (realHasAudio: boolean, videoUrl: string) => {
      hasAudio.value = realHasAudio && hasAudio.value;
      if (jessibucaRef.value) {
        console.log(jessibucaRef.value);
        jessibucaRef.value.play(videoUrl);
      } else {
        nextTick(() => {
          jessibucaRef.value?.play(videoUrl);
        });
      }
    };

    const close = () => {
      console.log("关闭视频");
      if (jessibucaRef.value) {
        jessibucaRef.value.pause();
      }
      baseService
        .post("/index/api/close_streams", {
          secret: app.media_secret,
          force: true
        })
        .then((res) => {
          console.log(res.data.count_closed !== 0 ? "已关闭" : "未关闭");
        });
    };

    const videoError = (e: any) => {
      console.log("播放器错误：" + JSON.stringify(e));
    };

    const getSelectedItem = () => {
      return (state.dataList as any).list.find((item: {
        streamUrl: string
      }) => item.streamUrl === forms.value.selectStreamUrl);
    };

    const setStreamInfo = async (channelId: string) => {
      if (isChanged.value) {
        close();
        jessibucaRef.value?.destroy();
      }
      isChanged.value = true;
      streamInfo.value = {
        streamServerIP: "",
        deviceID: "/live",
        channelId: "/" + channelId
      };

      const selectedCamera = (state.dataList as any).list.find((item: { streamUrl: string }) => item.streamUrl === forms.value.selectStreamUrl);
      deviceName.value = selectedCamera.name;
      cameraId.value = selectedCamera.camId;
      camPos.value = selectedCamera.lat + "," + selectedCamera.lng;
      // 新增：同步分组id和分组名称
      forms.value.gId = selectedCamera.gId || '';
      forms.value.gName = selectedCamera.gName || '';

      try {
        await mediaServerRegister(forms.value.selectStreamUrl, channelId);
        // 使用循环来等待媒体信息可用
        let mediaInfo = null;
        while (!mediaInfo) {
          try {
            mediaInfo = await mediaListInfo();
          } catch (error) {
            // 如果获取媒体信息失败,等待一段时间后重试
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }

        const info = mediaInfo.tracks;

        info.forEach((item: {
          codec_type: any;
          codec_id_name: string;
          fps: string;
          width: string;
          height: string;
          sample_rate: string;
          sample_bit: string
        }) => {
          if (!item.codec_type) videoInfo.value = item.codec_id_name + "/" + item.fps + "FPS/" + item.width + "×" + item.height;
          if (item.codec_type) audioInfo.value = item.codec_id_name + "/" + item.sample_rate + "Hz/" + item.sample_bit + "bit";
        });
      } catch (error) {
        console.error("Error processing media info:", error);
      }
      play(streamInfo.value, true);
      streamArrInfo.value = videoUrl.value;
    };

    const handleMouseDown = (event: MouseEvent) => {
      if (polygonLineArray.value.length < polygonLineMaxCount.value) {
        if (polygonLine.value === null) {
          const x = event.offsetX;
          const y = event.offsetY;
          polygonLine.value = new MyLine(x, y);
        }
      }
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (polygonLine.value) {
        const x = event.offsetX;
        const y = event.offsetY;

        reDrawVideoCanvas();
        polygonLine.value.LineUpdate(x, y);
        polygonLine.value.LineDraw(videoCanvasCtx.value!);
      }
    };

    const handleMouseUp = (event: MouseEvent) => {
      if (polygonLine.value) {
        reDrawVideoCanvas();
        let x = event.offsetX; // 使用 offsetX 获取相对于画布的坐标
        let y = event.offsetY; // 使用 offsetY 获取相对于画布的坐标

        if (polygonLineArray.value.length === polygonLineMaxCount.value - 1) {
          const firstLine = polygonLineArray.value[0];
          x = firstLine.x1;
          y = firstLine.y1;
          isDrawing.value = false;
        }

        polygonLine.value.LineUpdate(x, y);
        polygonLine.value.LineDraw(videoCanvasCtx.value!);
        polygonLineArray.value.push(polygonLine.value);

        if (polygonLineArray.value.length === polygonLineMaxCount.value) {
          polygonLine.value = null;

          // 获取画布的实际宽度和高度
          const canvasWidth = videoCanvasCtx.value!.canvas.width;
          const canvasHeight = videoCanvasCtx.value!.canvas.height;

          //现在要把下面的坐标转换为相对于摄像头尺寸的比例坐标
          // const cameraWidth = parseInt(videoInfo.value.split('/')[2].split('×')[0]);
          // const cameraHeight = parseInt(videoInfo.value.split('/')[2].split('×')[1]);

          //这里的坐标是相对于画布的坐标，需要转换为相对于摄像头尺寸的比例坐标
          let cameraWidth: number;
          let cameraHeight: number;
          //videoInfo可能为空，需要判断一下
          if (videoInfo.value) {
            cameraWidth = parseInt(videoInfo.value.split("/")[2].split("×")[0]);
            cameraHeight = parseInt(videoInfo.value.split("/")[2].split("×")[1]);
            cameraWidth = cameraWidth === 0 ? 1920 : cameraWidth;
            cameraHeight = cameraHeight === 0 ? 1080 : cameraHeight;
          }

          const convertedPolygonLineArray = polygonLineArray.value.map((line) => {
            const newLine = new MyLine(
              //这里的cameraWidth和cameraHeight为什么拿不到？
              Math.round((line.x1 / canvasWidth) * cameraWidth),
              Math.round((line.y1 / canvasHeight) * cameraHeight)
            );
            newLine.x2 = Math.round((line.x2! / canvasWidth) * cameraWidth);
            newLine.y2 = Math.round((line.y2! / canvasHeight) * cameraHeight);
            return newLine;
          });

          coordinatesInfo.value = JSON.stringify(convertedPolygonLineArray, null, 4);
          if (parsedCoordinatesInfo.value !== null) {
            forms.value.roi = convertToString(parsedCoordinatesInfo.value);
          }

          // 转换 MyLine 对象为 Segment 对象
          const segments: Segment[] = convertedPolygonLineArray.map((line) => ({
            x1: line.x1,
            y1: line.y1,
            x2: line.x2!,
            y2: line.y2!
          }));

          // 提取角的坐标并打印
          const cornersFlatList: number[] = extractCornersAsList(segments);
          console.log("坐标列表:", cornersFlatList);
          if (cornersFlatList.length == null || cornersFlatList.length === 0) {
            clearVideoCanvas();
            ElMessage({
              message: "坐标异常，请重新绘制",
              type: "warning"
            });
          } else {
            if (cornersFlatList[0] == 0 && cornersFlatList[1] == 0) {
              //确认框刷新
              ElMessageBox.alert("摄像头异常，请刷新页面", {
                // if you want to disable its autofocus
                // autofocus: false,
                confirmButtonText: "确认",
                callback: (action: Action) => {
                  if (action === "confirm") {
                    location.reload();
                  }
                }
              });
            }
          }
          forms.value.roi = JSON.stringify(cornersFlatList);
        } else {
          polygonLine.value = new MyLine(x, y);
        }

        console.log("mouseup() polygonLineArray.length =", polygonLineArray.value.length);
        console.log(polygonLineArray.value);
        if (polygonLineArray.value.length === 1) {
          polygonLineArray.value.forEach((line) => {
            line.x2 = line.x1;
            line.y2 = line.y1;
          });
          if (polygonLine.value) {
            polygonLine.value.x1 = polygonLineArray.value[0].x1;
            polygonLine.value.y1 = polygonLineArray.value[0].y1;
          }
        }
      }
    };

    const dataSubmit = () => {
      dataFormRef.value.validate((valid: boolean) => {
        if (!valid) {
          return;
        }

        const algoId = forms.value.trainName[0];
        // 从实时查询的配置中获取算法名称
        const selectedConfig = forms.value.list && forms.value.list.length > 0 ? forms.value.list[0] : null;
        const nameToSend = selectedConfig ? selectedConfig.trainName : "";

        // 使用实时查询到的配置中的 model 字段
        const modelToSend = forms.value.model || "";

        console.log("=== 提交数据日志（实时查询模式）===");
        console.log("选中的算法ID:", algoId);
        console.log("实时查询的配置:", selectedConfig);
        console.log("算法名称 (trainName):", nameToSend);
        console.log("forms中的model字段:", forms.value.model);
        console.log("最终使用的model:", modelToSend);
        console.log("==================");

        const payload = {
          trainName: nameToSend,
          sendUrl: forms.value.sendUrl,
          threshold: parseFloat(forms.value.threshold),
          sname: forms.value.sname,
          timeInter: parseInt(forms.value.timeInter),
          analysisFrame: parseInt(forms.value.analysisFrame),
          deviceId: deviceName.value,
          cameraId: cameraId.value,
          cameraLocal: forms.value.cameraLocal,
          roi: forms.value.roi,
          modelAfterTrainingUrl: forms.value.modelAfterTrainingUrl,
          nodeId: forms.value.nodeId,
          videoInput: forms.value.selectStreamUrl,
          containerId: forms.value.containerId,
          predictPath: forms.value.predictPath,
          outputStream: forms.value.outputStream,
          nodeName: forms.value.nodeName,
          nodeIp: forms.value.nodeIp,
          list: forms.value.list,
          selectStreamUrlList: forms.value.selectStreamUrlList,
          tenantId: forms.value.tenantId ? parseInt(forms.value.tenantId) : null,
          gId: forms.value.gId,
          gName: forms.value.gName,
          // DTO新增字段
          platform: forms.value.platform,
          securityLevel: forms.value.securityLevel,
          openaiBaseUrl: forms.value.openaiBaseUrl,
          model: modelToSend, // 使用从 ImagesInfo 获取的 model
          openApiKey: forms.value.openApiKey,
          prompt: forms.value.prompt,
          imageId: forms.value.imageId
        };

        console.log("完整的提交payload:", payload);

        if (route.query.id === undefined) {
          loading.value = true;
          // console.log("部署", forms);
          baseService
            .post("/edge/predictdeploy", payload)
            .then((res) => {
              loading.value = false;
              ElMessage.success({
                message: "成功",
                duration: 500
              });
              //清空表单
              forms.value = {
                selectStreamUrl: "",
                modelUrl: "",
                modelName: "",
                sendUrl: "",
                selectModel: "",
                roi: "[220,500,750,500,710,710,180,710]",
                sname: "",
                modelAfterTrainingUrl: "",
                threshold: "0.6",
                timeInter: "10",
                analysisFrame: "30",
                trainName: "",
                nodeId: "",
                deviceName: "",
                cameraId: "",
                modelBaseName: "",
                outputStream: "",
                nodeName: "",
                containerId: "",
                predictPath: "",
                securityLevel: "",
                camPos: "",
                cameraLocal: "['xzu','91.18577','29.64932']",
                nodeIp: "",
                list: [],
                selectStreamUrlList: [],
                tenantId: "",
                gId: "",
                gName: "",
                platform: "",
                openaiBaseUrl: "",
                openApiKey: "",
                prompt: "",
                model: "",
                imageId: ""
              };
              selectedStreamUrlList.value = [];
              (deviceName.value = ""), (cameraId.value = ""), (streamArrInfo.value = "");
              camPos.value = "";
              videoUrl.value = "";
              //清空绘制的线
              clearVideoCanvas();
              router.push("/edge/predictdeploy");
            })
            .catch((error) => {
              loading.value = false;
              ElMessage.error({
                message: "失败",
                duration: 500
              });
              console.log(error);
            });
        } else {
          //编辑逻辑
          // console.log('编辑');
          //编辑的put请求
          baseService
            .put("/edge/predictdeploy", {
              id: route.query.id,
              ...payload
            })
            .then((res) => {
              ElMessage.success({
                message: "成功",
                duration: 500
              });
              router.push("/edge/predictdeploy");
            });
          //打印一下看看有没有id
          // console.log(route.query.id);
        }
      });
    };

    const handleChange = (value: string) => {
      const selectedItem = modelInfo.value.find((item) => item.modelBaseName === value);
      if (selectedItem) {
        forms.value.modelName = selectedItem.modelBaseName;
        forms.value.nodeId = selectedItem.nodeId;
        forms.value.modelAfterTrainingUrl = selectedItem.modelAfterTrainingUrl;
      }
    };

    const startDraw = () => {
      isDrawing.value = true;
      adjustVideoCanvas();

      videoCanvas.value!.addEventListener("mousedown", handleMouseDown);
      videoCanvas.value!.addEventListener("mousemove", handleMouseMove);
      videoCanvas.value!.addEventListener("mouseup", handleMouseUp);
    };

    const adjustVideoCanvas = () => {
      const w = videoCanvasBox.value.offsetWidth - 1.8;
      const h = videoCanvasBox.value.offsetHeight - 2.2;
      videoCanvas.value!.setAttribute("width", w.toString());
      videoCanvas.value!.setAttribute("height", h.toString());
      clearVideoCanvas();
    };

    const clearVideoCanvas = () => {
      videoCanvas.value!.removeEventListener("mousedown", handleMouseDown);
      videoCanvas.value!.removeEventListener("mousemove", handleMouseMove);
      videoCanvas.value!.removeEventListener("mouseup", handleMouseUp);
      videoCanvasCtx.value!.clearRect(0, 0, videoCanvas.value!.width, videoCanvas.value!.height);
      polygonLineArray.value = [];
    };

    const reDrawVideoCanvas = () => {
      videoCanvasCtx.value!.clearRect(0, 0, videoCanvas.value!.width, videoCanvas.value!.height);
      polygonLineArray.value.forEach((line) => line.LineDraw(videoCanvasCtx.value!));
    };

    //多选摄像头
    const multiple = (value: any) => {
      forms.value.selectStreamUrl = "";
      const result = ref<any>([]);
      let lastGId = '';
      let lastGName = '';
      value.forEach((items: any) => {
        const res = (state.dataList as any).list?.find((item: { streamUrl: string }) => item.streamUrl === items);
        result.value.push({
          videoInput: items,
          id: res?.id,
          deviceName: res?.name,
          cameraId: res?.camId,
          camPos: res?.lat + "," + res?.lng,
          roi: "[220,500,750,500,710,710,180,710]",
          streamArrInfo: "/live/" + res?.id + ".live.flv"
        });
        // 记录最后一个分组信息（如需支持多分组可自行扩展）
        lastGId = res?.gId || '';
        lastGName = res?.gName || '';
      });
      forms.value.selectStreamUrlList = result.value;
      // 新增：同步分组id和分组名称（以最后一个为准）
      forms.value.gId = lastGId;
      forms.value.gName = lastGName;
    };
    const playStart = () => {
      //如果forms.value.selectStreamUrl包含amp;则去掉
      if (forms.value.selectStreamUrl.includes("amp;")) {
        forms.value.selectStreamUrl = forms.value.selectStreamUrl.replace("amp;", "");
      }
      setStreamInfo(getSelectedItem().id);

      jessibucaRef.value?.play(videoUrl.value);
    };

    const playStop = () => {
      jessibucaRef.value?.pause();
    };

    const popoverStyles = {
      backgroundColor: "#f5f5f5",
      width: "25vw"
    };
    const forms = ref<Forms>({
      selectStreamUrl: "",
      modelUrl: "",
      modelName: "",
      sendUrl: "",
      selectModel: "",
      roi: "[220,500,750,500,710,710,180,710]",
      sname: "",
      threshold: "0.6",
      timeInter: "10",
      analysisFrame: "30",
      trainName: "",
      modelAfterTrainingUrl: "",
      nodeId: "",
      modelBaseName: "",
      outputStream: "",
      nodeName: "",
      containerId: "",
      predictPath: "",
      securityLevel: "",
      deviceName: "",
      cameraId: "",
      camPos: "",
      cameraLocal: "['xzu','91.18577','29.64932']",
      nodeIp: "",
      list: [],
      selectStreamUrlList: [],
      tenantId: "",
      gId: "",
      gName: "",
      platform: "",
      openaiBaseUrl: "",
      openApiKey: "",
      prompt: "",
      model: "",
      imageId: ""
    });

    if (route.query.id !== undefined) {
      istrainName.value = true;
      isMutiple.value = false;
      // 这里根据id查询数据并且赋值给forms
      baseService.get("/edge/predictdeploy/" + route.query.id).then((res) => {
        const data = res.data;

        // 直接从后端数据填充表单
        forms.value.sendUrl = data.sendUrl;
        forms.value.threshold = data.threshold;
        forms.value.sname = data.sname;
        forms.value.timeInter = data.timeInter;
        forms.value.analysisFrame = data.analysisFrame;
        forms.value.cameraLocal = data.cameraLocal;
        forms.value.roi = data.roi;
        forms.value.modelAfterTrainingUrl = data.modelAfterTrainingUrl;
        forms.value.nodeId = data.nodeId;
        forms.value.selectStreamUrl = data.videoInput;
        forms.value.containerId = data.containerId;
        forms.value.predictPath = data.predictPath;
        forms.value.outputStream = data.outputStream;
        forms.value.gId = data.gId;
        forms.value.gName = data.gName;

        deviceName.value = data.deviceId;
        cameraId.value = data.cameraId;
        camPos.value = data.cameraLocal;

        // 编辑模式：直接使用后端返回的数据，不需要通过算法名称查找
        // 如果有imageId，直接设置trainName并触发实时查询
        if (data.imageId) {
          forms.value.trainName = [data.imageId];
          // 由于是编辑模式，我们已经有完整的配置数据，可以直接使用
          // 但仍然调用onSelectImages以保持一致性和触发相关逻辑
          onSelectImages([data.imageId]);
        } else if (data.trainName) {
          // 兼容旧数据：通过算法名称查找
          const algo = imageInfo.value.find((i) => i.imgName === data.trainName);
          if (algo) {
            forms.value.trainName = [algo.id];
            onSelectImages([algo.id]);
          }
        }

        // 覆盖从onSelectImages设置的字段，使用从后端获取的精确值
        forms.value.modelBaseName = data.model || forms.value.modelBaseName;
        forms.value.model = data.model || forms.value.model; // 优先使用后端数据
        forms.value.securityLevel = data.securityLevel || forms.value.securityLevel;
        forms.value.platform = data.platform || forms.value.platform; // 优先使用后端数据
        forms.value.openaiBaseUrl = data.openaiBaseUrl || forms.value.openaiBaseUrl;
        forms.value.openApiKey = data.openApiKey || forms.value.openApiKey;
        forms.value.prompt = data.prompt || forms.value.prompt;
        forms.value.imageId = data.imageId || forms.value.imageId;
        forms.value.list = data.list || forms.value.list;
        forms.value.nodeName = data.nodeName || forms.value.nodeName;

        console.log("编辑模式加载数据完成:", {
          model: forms.value.model,
          platform: forms.value.platform,
          securityLevel: forms.value.securityLevel,
          backendData: data
        });
      });
    }
    if (route.query.streamUrl !== undefined) {
      (forms.value.selectStreamUrl as any) = route.query.streamUrl;
      setTimeout(() => {
        setStreamInfo(route.query.tid as string);
      }, 1500);
    }

    const onSelectPush = (value: any) => {
      let it = pushInfo.value.find((item) => {
        return item.id == value;
      });
      if (it?.sprotoName) {
        forms.value.sendUrl = it.sprotoName || "";
      }
    };
    onActivated(() => {
      if (history.state.urls == undefined) {
        isShow.value = false;
        selectedStreamUrlList.value = [];
        return;
      }
      isShow.value = true;
      selectedStreamUrlList.value = history.state.urls;
      multiple(selectedStreamUrlList.value);
    });

    const onSelectPush2 = (value: any) => {
      // if (value !== minArr.value.hostId) {
      //   ElMessage.warning("温馨提示：此选项已做负载均衡，默认选择为最佳节点哟！");
      // }
      let it = nodeInfo.value.find((item) => {
        return item.hostId == value;
      });
      if (it?.hostId) {
        // console.log(it);

        forms.value.nodeName = it.hostname || "";
        forms.value.nodeIp = it.ipv4Addr || "";
      }
    };

    return {
      onSelectImages,
      imageInfo,
      popoverStyles,
      onSelectPush,
      onSelectPush2,
      nodeInfo,
      modelInfo,
      deviceName,
      cameraId,
      forms,
      jessibucaRef,
      videoUrl,
      tabActiveName,
      channelId,
      streamId,
      mediaServerId,
      streamInfo,
      videoInfo,
      audioInfo,
      streamArrInfo,
      coordinatesInfo,
      hasAudio,
      isChanged,
      isDrawing,
      polygonLineArray,
      polygonLineMaxCount,
      polygonLine,
      videoCanvas,
      videoCanvasCtx,
      videoCanvasBox,
      state,
      rules,
      camPos,
      handleChange,
      dataSubmit,
      play,
      playFromStreamInfo,
      close,
      videoError,
      getSelectedItem,
      setStreamInfo,
      handleMouseDown,
      handleMouseMove,
      handleMouseUp,
      startDraw,
      adjustVideoCanvas,
      clearVideoCanvas,
      reDrawVideoCanvas,
      playStart,
      playStop,
      isShow,
      isShow2,
      pushInfo,
      alarmTypeInfo,
      dataFormRef,
      istrainName,
      islevel,
      isMutiple,
      loading,
      multiple,
      selectedStreamUrlList,
      deptName,
      logo
    };
  }
});
</script>

<style>
#jessibuca:focus {
  outline: -webkit-focus-ring-color auto 0px;
}

.videoCanvas {
  margin: 0 0;
  position: absolute;
  top: 0;
  /*width: 800px;*/
  /*height: 400px;*/
  z-index: 1;
}

.el-button + .el-button {
  margin-left: 8px;
}

.video-seletion-tip {
  text-align: center;
  font-size: 20px;
  width: 100%;
  margin-top: 33%;
  color: #cccccc;
}
</style>
